# 路浩智能电子签技术架构设计文档
## 基于PRD-路浩-augment-v4的完整技术实现方案

**文档版本**: V4.0  
**编制日期**: 2025年6月  
**编制人**: 技术架构团队  
**审核人**: 技术负责人  

---

## 需求理解

### 1. PRD概述
路浩智能电子签是一个以AI技术为核心驱动的智能合同全生命周期管理平台，支持个人用户和企业用户的电子签名需求，提供合同生成、审查、签署、归档、管理等全流程服务。

### 2. 功能列表摘要  
- **个人版功能**: 账户认证、签名管理、合同签署、移动端适配
- **企业版功能**: 组织权限管理、AI智能服务、合同生命周期管理、印章管理、存证服务
- **AI能力**: 智能合同生成、合同审查、OCR识别、自然语言搜索
- **运营后台**: 用户管理、内容管理、财务管理、系统监控
- **基础设施**: 容器化部署、监控运维、安全合规

### 3. 关键业务流程
用户注册认证 → 合同创建/AI生成 → 审批流程 → 多方签署 → 存证归档 → 后续管理

## 项目基础信息

**业务规模预期：**
- 预期用户量：100万+ 注册用户，10万+ DAU
- 并发要求：峰值10,000+ QPS  
- 数据规模：百万级合同数据，10TB级文件存储
- 可用性要求：99.9%

## 技术栈约束

### 后端技术栈
- **微服务框架：** 
  - Java：Spring Boot 3.x + Spring Cloud Alibaba
  - Go：Kratos框架
- **数据存储：** 
  - 关系型数据库：PostgreSQL 15+
  - 文档数据库：MongoDB 7.x
  - 缓存：Redis 7.x
  - 数据仓库：ClickHouse
- **中间件组件：**
  - 消息队列：RabbitMQ + Kafka
  - 服务注册发现：Nacos
  - 任务调度：XXL-Job
  - 搜索引擎：ElasticSearch 8.x

### 前端技术栈
- **构建工具：** Vite + Webpack
- **核心框架：** React + Next.js
- **样式方案：** Tailwind CSS
- **UI组件库：** Ant Design React + Chakra UI + ShadCN UI
- **状态管理：** Redux + Zustand
- **跨端开发：** Taro + UniApp

### 基础设施
- **容器技术：** Docker + Kubernetes
- **负载均衡：** SLB
- **链路追踪：** Zipkin
- **监控告警：** Prometheus + Grafana + Zabbix
- **日志系统：** ELK Stack + SLS

## 技术架构设计

### 1. 系统整体架构

**架构风格选择**：采用微服务架构，基于领域驱动设计(DDD)进行服务拆分，确保高内聚、低耦合。选择微服务的原因：
1. 业务复杂度高，需要独立演进
2. 团队规模大，需要并行开发
3. 技术栈多样化，Java+Go混合架构
4. 扩展性要求高，需要独立扩缩容

**系统组件图**：

```mermaid
graph TB
    subgraph "用户接入层"
        A1[PC Web管理端<br/>React+Next.js]
        A2[H5移动签署端<br/>React+Vite]
        A3[微信小程序<br/>Taro]
    end

    subgraph "API网关层"
        B1[Spring Cloud Gateway<br/>路由+限流+认证]
    end

    subgraph "Java微服务层"
        C1[用户服务<br/>user-service]
        C2[企业服务<br/>enterprise-service]
        C3[组织权限服务<br/>org-service]
        C4[合同服务<br/>contract-service]
        C5[印章服务<br/>seal-service]
        C6[计费服务<br/>billing-service]
        C7[审批流服务<br/>workflow-service]
    end

    subgraph "Go微服务层"
        D1[AI服务<br/>ai-service]
        D2[文件服务<br/>file-service]
        D3[通知服务<br/>notification-service]
        D4[存证服务<br/>evidence-service]
        D5[网关服务<br/>gateway-service]
    end

    subgraph "数据存储层"
        E1[PostgreSQL<br/>主业务数据]
        E2[MongoDB<br/>日志+文档]
        E3[Redis<br/>缓存+会话]
        E4[ClickHouse<br/>数据分析]
        E5[ElasticSearch<br/>全文搜索]
        E6[MinIO<br/>对象存储]
    end

    subgraph "中间件层"
        F1[RabbitMQ<br/>业务消息]
        F2[Kafka<br/>日志流]
        F3[Nacos<br/>配置中心]
        F4[XXL-Job<br/>任务调度]
    end

    subgraph "第三方服务"
        G1[CA数字证书]
        G2[时间戳服务]
        G3[区块链存证]
        G4[短信邮件]
        G5[大模型API]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B1 --> C6
    B1 --> C7
    
    B1 --> D1
    B1 --> D2
    B1 --> D3
    B1 --> D4
    
    C1 --> E1
    C2 --> E1
    C3 --> E1
    C4 --> E1
    C5 --> E1
    C6 --> E1
    C7 --> E1
    
    D1 --> E2
    D2 --> E6
    D3 --> E3
    D4 --> E5
    
    C4 --> F1
    C5 --> F1
    D3 --> F1
    
    D1 --> G5
    C4 --> G1
    C4 --> G2
    D4 --> G3
    D3 --> G4
```

**服务拆分策略**：
- **Java服务**：处理复杂业务逻辑，事务管理，企业级功能
  - 用户服务：用户注册、认证、个人信息管理
  - 企业服务：企业认证、企业信息管理
  - 组织权限服务：RBAC权限、组织架构管理
  - 合同服务：合同生命周期管理、签署流程
  - 印章服务：印章管理、用印审批
  - 计费服务：订单、支付、发票管理
  - 审批流服务：工作流引擎、审批管理

- **Go服务**：处理高并发、高性能场景
  - AI服务：智能合同生成、审查、OCR识别
  - 文件服务：文件上传下载、格式转换
  - 通知服务：消息推送、短信邮件
  - 存证服务：区块链存证、证据链生成
  - 网关服务：API网关、限流熔断

**API网关设计**：
- 统一入口：所有外部请求通过Spring Cloud Gateway
- 路由规则：基于路径和服务名进行路由
- 限流熔断：基于令牌桶算法实现限流，Hystrix实现熔断
- 认证授权：JWT Token验证，RBAC权限控制

**服务通信**：
- 同步调用：HTTP REST API + OpenFeign
- 异步消息：RabbitMQ实现服务解耦
- 配置管理：Nacos统一配置管理
- 服务注册发现：Nacos服务注册中心

### 2. 技术选型

#### 后端服务选型
**Java (Spring Boot) vs Go (Kratos)**：
- **Java服务**：适合复杂业务逻辑、事务处理、企业级功能
  - 生态成熟，Spring框架强大
  - 事务管理完善，适合金融级业务
  - 开发效率高，团队技能匹配
- **Go服务**：适合高并发、高性能、基础设施类服务
  - 并发性能优异，内存占用低
  - 编译速度快，部署简单
  - 适合AI服务、文件处理等场景

**数据库选型**：
- **PostgreSQL**：主业务数据存储
  - ACID事务支持，数据一致性强
  - JSON/JSONB支持，灵活性好
  - 扩展性强，支持复杂查询
- **MongoDB**：文档数据存储
  - Schema-less，适合日志和版本数据
  - 写入性能高，适合大量日志
  - 聚合查询能力强
- **Redis**：缓存策略设计
  - 多级缓存：L1本地缓存 + L2 Redis缓存
  - 缓存模式：Cache-Aside模式
  - 过期策略：TTL + LRU
- **ClickHouse**：数据分析场景
  - 列式存储，分析性能优异
  - 压缩比高，存储成本低
  - 支持实时数据分析

**消息队列选型**：
- **RabbitMQ**：实时业务消息场景
  - 可靠性高，支持事务消息
  - 路由灵活，支持多种交换机
  - 延迟队列，死信队列完善
- **Kafka**：大数据流处理场景
  - 高吞吐量，适合日志收集
  - 分区机制，支持水平扩展
  - 持久化存储，数据不丢失

#### 前端技术栈整合
**构建工具整合**：
- **Vite**：开发环境，热更新快
- **Webpack**：生产环境，打包优化

**UI组件库策略**：
- **Ant Design**：企业级管理后台，组件丰富
- **Chakra UI**：现代化用户界面，定制性强
- **ShadCN UI**：高度定制化组件，与Tailwind完美结合

**状态管理方案**：
- **Redux**：复杂状态管理，企业级应用
- **Zustand**：轻量级状态管理，简单场景

**跨端开发策略**：
- **Taro**：微信生态应用，小程序优先
- **UniApp**：多平台小程序和App，一码多端

### 3. 核心模块设计

**合同生命周期管理模块**：

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 合同服务
    participant A as AI服务
    participant W as 审批流服务
    participant S as 印章服务
    participant E as 存证服务
    participant N as 通知服务

    U->>C: 1. 创建合同
    C->>A: 2. AI生成/审查
    A-->>C: 3. 返回合同内容
    C->>W: 4. 提交审批
    W-->>C: 5. 审批通过
    C->>S: 6. 申请用印
    S-->>C: 7. 用印授权
    C->>U: 8. 发起签署
    U->>C: 9. 完成签署
    C->>E: 10. 生成存证
    C->>N: 11. 发送通知
    N-->>U: 12. 签署完成通知
```

**AI智能服务模块**：

```mermaid
graph LR
    A[用户输入] --> B[意图识别]
    B --> C[知识库检索]
    C --> D[RAG增强]
    D --> E[LLM生成]
    E --> F[后处理优化]
    F --> G[返回结果]
    
    H[向量数据库] --> C
    I[法律知识库] --> C
    J[合同模板库] --> C
```

## 数据库设计

### 1. 数据模型

**ER图设计**：

```mermaid
erDiagram
    USERS {
        bigint id PK "用户ID"
        varchar mobile UK "手机号"
        varchar email "邮箱"
        varchar real_name "真实姓名"
        varchar id_card "身份证号"
        integer status "状态"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
    
    ENTERPRISES {
        bigint id PK "企业ID"
        varchar name "企业名称"
        varchar credit_code UK "统一社会信用代码"
        varchar legal_person "法人姓名"
        integer auth_status "认证状态"
        bigint super_admin_id FK "超管用户ID"
        timestamp created_at "创建时间"
    }
    
    ORG_NODES {
        bigint id PK "组织节点ID"
        bigint enterprise_id FK "企业ID"
        bigint parent_id "父节点ID"
        varchar name "节点名称"
        integer node_type "节点类型"
        bigint user_id FK "用户ID"
        timestamp created_at "创建时间"
    }
    
    CONTRACTS {
        bigint id PK "合同ID"
        varchar title "合同标题"
        bigint enterprise_id FK "企业ID"
        bigint initiator_id FK "发起人ID"
        integer status "合同状态"
        varchar template_id "模板ID"
        timestamp deadline "截止时间"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }
    
    CONTRACT_SIGNERS {
        bigint id PK "签署方ID"
        bigint contract_id FK "合同ID"
        integer signer_type "签署方类型"
        bigint signer_id "签署主体ID"
        bigint actor_user_id FK "经办人ID"
        integer sign_order "签署顺序"
        integer sign_status "签署状态"
        timestamp signed_at "签署时间"
    }
    
    SEALS {
        bigint id PK "印章ID"
        bigint owner_id "所有者ID"
        integer seal_type "印章类型"
        varchar name "印章名称"
        varchar file_path "文件路径"
        integer status "状态"
        timestamp created_at "创建时间"
    }
    
    USERS ||--o{ ENTERPRISES : "管理"
    ENTERPRISES ||--o{ ORG_NODES : "包含"
    USERS ||--o{ ORG_NODES : "属于"
    ENTERPRISES ||--o{ CONTRACTS : "发起"
    USERS ||--o{ CONTRACTS : "创建"
    CONTRACTS ||--o{ CONTRACT_SIGNERS : "包含"
    USERS ||--o{ CONTRACT_SIGNERS : "签署"
    USERS ||--o{ SEALS : "拥有"
    ENTERPRISES ||--o{ SEALS : "拥有"
```

### 2. 核心表结构DDL

**PostgreSQL核心表设计**：

```sql
-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    mobile VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(100),
    real_name VARCHAR(50),
    id_card VARCHAR(18),
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 企业表
CREATE TABLE enterprises (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    credit_code VARCHAR(50) UNIQUE NOT NULL,
    legal_person VARCHAR(50),
    auth_status INTEGER DEFAULT 1,
    super_admin_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 组织节点表
CREATE TABLE org_nodes (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id BIGINT NOT NULL REFERENCES enterprises(id),
    parent_id BIGINT DEFAULT 0,
    name VARCHAR(50) NOT NULL,
    node_type INTEGER NOT NULL,
    user_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 合同表
CREATE TABLE contracts (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    enterprise_id BIGINT NOT NULL REFERENCES enterprises(id),
    initiator_id BIGINT NOT NULL REFERENCES users(id),
    status INTEGER DEFAULT 1,
    template_id VARCHAR(64),
    deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 合同签署方表
CREATE TABLE contract_signers (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL REFERENCES contracts(id),
    signer_type INTEGER NOT NULL,
    signer_id BIGINT NOT NULL,
    actor_user_id BIGINT REFERENCES users(id),
    sign_order INTEGER DEFAULT 0,
    sign_status INTEGER DEFAULT 0,
    signed_at TIMESTAMP
);

-- 印章表
CREATE TABLE seals (
    id BIGSERIAL PRIMARY KEY,
    owner_id BIGINT NOT NULL,
    seal_type INTEGER NOT NULL,
    name VARCHAR(50) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_mobile ON users(mobile);
CREATE INDEX idx_enterprises_credit_code ON enterprises(credit_code);
CREATE INDEX idx_org_nodes_enterprise_id ON org_nodes(enterprise_id);
CREATE INDEX idx_contracts_enterprise_id ON contracts(enterprise_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contract_signers_contract_id ON contract_signers(contract_id);
CREATE INDEX idx_seals_owner_id ON seals(owner_id);
```

### 3. 数据架构详细设计

#### MongoDB设计
**文档结构设计**：
```javascript
// 合同操作日志集合
db.contract_logs.insertOne({
    _id: ObjectId(),
    contract_id: "123456789",
    operation_type: "sign",
    operator_id: "user_123",
    operator_name: "张三",
    operation_detail: {
        sign_position: {x: 100, y: 200},
        seal_id: "seal_456",
        ip_address: "***********"
    },
    timestamp: ISODate(),
    created_at: ISODate()
});

// 合同版本历史集合
db.contract_versions.insertOne({
    _id: ObjectId(),
    contract_id: "123456789",
    version: 2,
    file_hash: "sha256_hash_value",
    file_path: "/contracts/v2/contract_123.pdf",
    changes: [
        {
            type: "signature_added",
            signer: "user_123",
            position: {page: 1, x: 100, y: 200}
        }
    ],
    created_at: ISODate()
});
```

#### Redis设计
**缓存架构设计**：
```yaml
# 多级缓存策略
L1_Cache: # 本地缓存 (Caffeine)
  - 用户权限信息 (TTL: 5分钟)
  - 企业基础信息 (TTL: 10分钟)
  - 系统配置信息 (TTL: 30分钟)

L2_Cache: # Redis缓存
  - 用户会话信息 (TTL: 2小时)
  - 合同状态信息 (TTL: 1小时)
  - 印章授权信息 (TTL: 30分钟)
  - 热点数据缓存 (TTL: 1天)

# Redis数据结构使用场景
String: # 简单键值对
  - "user:session:{token}" -> "user_info_json"
  - "contract:status:{id}" -> "status_value"

Hash: # 对象存储
  - "user:info:{id}" -> {name, email, status}
  - "enterprise:info:{id}" -> {name, auth_status}

Set: # 集合操作
  - "user:permissions:{id}" -> {perm1, perm2, perm3}
  - "contract:signers:{id}" -> {user1, user2, user3}

ZSet: # 排序集合
  - "contract:deadline" -> {contract_id: timestamp}
  - "user:activity" -> {user_id: last_active_time}

List: # 队列操作
  - "notification:queue" -> [msg1, msg2, msg3]
  - "task:pending" -> [task1, task2, task3]
```

#### ClickHouse设计
**数据模型设计**：
```sql
-- 用户行为分析表
CREATE TABLE user_behavior_log (
    event_time DateTime,
    user_id UInt64,
    enterprise_id UInt64,
    event_type String,
    event_detail String,
    ip_address String,
    user_agent String,
    created_date Date DEFAULT toDate(event_time)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_date)
ORDER BY (created_date, user_id, event_time);

-- 合同统计分析表
CREATE TABLE contract_statistics (
    stat_date Date,
    enterprise_id UInt64,
    contract_count UInt32,
    signed_count UInt32,
    rejected_count UInt32,
    total_amount Decimal(15,2),
    avg_sign_duration UInt32
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(stat_date)
ORDER BY (stat_date, enterprise_id);

-- 物化视图用于实时统计
CREATE MATERIALIZED VIEW contract_daily_stats
TO contract_statistics
AS SELECT
    toDate(created_at) as stat_date,
    enterprise_id,
    count() as contract_count,
    countIf(status = 3) as signed_count,
    countIf(status = 6) as rejected_count,
    sum(total_amount) as total_amount,
    avg(sign_duration) as avg_sign_duration
FROM contracts
GROUP BY stat_date, enterprise_id;
```

### 4. 消息队列架构

#### RabbitMQ设计
**Exchange和Queue设计**：
```yaml
# 合同状态变更
contract_exchange:
  type: topic
  routing_keys:
    - "contract.created"
    - "contract.signed"
    - "contract.completed"
    - "contract.rejected"
  queues:
    - notification_queue: # 通知服务
        routing_key: "contract.*"
    - webhook_queue: # Webhook回调
        routing_key: "contract.completed"
    - statistics_queue: # 统计分析
        routing_key: "contract.*"

# 用印审批流程
seal_exchange:
  type: direct
  routing_keys:
    - "seal.approval.request"
    - "seal.approval.approved"
    - "seal.approval.rejected"
  queues:
    - seal_approval_queue:
        routing_key: "seal.approval.request"
    - seal_notification_queue:
        routing_key: "seal.approval.*"

# AI任务处理
ai_exchange:
  type: direct
  routing_keys:
    - "ai.contract.generate"
    - "ai.contract.review"
    - "ai.ocr.extract"
  queues:
    - ai_generation_queue:
        routing_key: "ai.contract.generate"
    - ai_review_queue:
        routing_key: "ai.contract.review"
    - ai_ocr_queue:
        routing_key: "ai.ocr.extract"
```

#### Kafka设计
**Topic设计**：
```yaml
# 日志收集Topic
application_logs:
  partitions: 12
  replication_factor: 3
  retention_ms: ********* # 7天
  use_case: "应用日志收集"

# 用户行为分析
user_behavior:
  partitions: 6
  replication_factor: 3
  retention_ms: ********** # 30天
  use_case: "用户行为数据分析"

# 审计日志
audit_logs:
  partitions: 3
  replication_factor: 3
  retention_ms: *********** # 1年
  use_case: "安全审计日志"

# 实时数据同步
data_sync:
  partitions: 6
  replication_factor: 3
  retention_ms: ******** # 1天
  use_case: "数据库间同步"
```

### 5. 中间件集成

#### Nacos集成
**服务注册发现**：
```yaml
# 服务注册配置
spring:
  cloud:
    nacos:
      discovery:
        server-addr: nacos-cluster:8848
        namespace: production
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        weight: 1
        healthy: true
        enabled: true
        ephemeral: true
        metadata:
          version: ${project.version}
          zone: ${deployment.zone}
```

**配置管理**：
```yaml
# 动态配置示例
# Data ID: contract-service-prod.yml
contract:
  signing:
    timeout: 7200000 # 2小时
    max_signers: 10
    auto_reminder: true
    reminder_interval: ******** # 1天

  ai:
    enabled: true
    model: "qwen-max"
    max_tokens: 4000
    temperature: 0.7

  storage:
    provider: "minio"
    bucket: "contracts"
    cdn_domain: "https://cdn.example.com"
```

#### XXL-Job任务调度
**任务类型设计**：
```java
// 合同到期提醒任务
@XxlJob("contractDeadlineReminder")
public void contractDeadlineReminder() {
    // 查询即将到期的合同
    List<Contract> expiringSoon = contractService.findExpiringSoon();

    // 发送提醒通知
    for (Contract contract : expiringSoon) {
        notificationService.sendDeadlineReminder(contract);
    }
}

// 数据统计任务
@XxlJob("dailyStatistics")
public void dailyStatistics() {
    // 生成昨日统计数据
    Date yesterday = DateUtils.addDays(new Date(), -1);
    statisticsService.generateDailyStats(yesterday);
}

// 文件清理任务
@XxlJob("cleanupTempFiles")
public void cleanupTempFiles() {
    // 清理临时文件
    fileService.cleanupTempFiles(7); // 清理7天前的临时文件
}
```

#### ElasticSearch集成
**索引设计**：
```json
{
  "mappings": {
    "properties": {
      "contract_id": {"type": "keyword"},
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "content": {
        "type": "text",
        "analyzer": "ik_max_word",
        "search_analyzer": "ik_smart"
      },
      "enterprise_id": {"type": "keyword"},
      "status": {"type": "integer"},
      "signers": {
        "type": "nested",
        "properties": {
          "user_id": {"type": "keyword"},
          "name": {"type": "keyword"},
          "sign_status": {"type": "integer"}
        }
      },
      "created_at": {"type": "date"},
      "updated_at": {"type": "date"}
    }
  },
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "ik_max_word": {
          "type": "ik_max_word"
        },
        "ik_smart": {
          "type": "ik_smart"
        }
      }
    }
  }
}
```

## 前端架构设计

### Web端架构
**构建工具策略**：
```javascript
// Vite配置 (开发环境)
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd'],
          utils: ['lodash', 'dayjs']
        }
      }
    }
  }
});

// Webpack配置 (生产环境)
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  },
  plugins: [
    new CompressionPlugin(),
    new BundleAnalyzerPlugin()
  ]
};
```

**UI组件库整合**：
```typescript
// 组件库整合策略
import { ConfigProvider } from 'antd';
import { ChakraProvider } from '@chakra-ui/react';
import { ThemeProvider } from 'styled-components';

// 主题配置
const theme = {
  antd: {
    token: {
      colorPrimary: '#1890ff',
      borderRadius: 6,
    }
  },
  chakra: {
    colors: {
      brand: {
        50: '#e3f2fd',
        500: '#1890ff',
        900: '#0d47a1',
      }
    }
  }
};

// 应用根组件
function App() {
  return (
    <ConfigProvider theme={theme.antd}>
      <ChakraProvider theme={theme.chakra}>
        <ThemeProvider theme={theme}>
          <Router />
        </ThemeProvider>
      </ChakraProvider>
    </ConfigProvider>
  );
}
```

**状态管理方案**：
```typescript
// Redux配置 (复杂状态)
import { configureStore } from '@reduxjs/toolkit';
import { userSlice } from './slices/userSlice';
import { contractSlice } from './slices/contractSlice';

export const store = configureStore({
  reducer: {
    user: userSlice.reducer,
    contract: contractSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST']
      }
    })
});

// Zustand配置 (轻量状态)
import { create } from 'zustand';

interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export const useUIStore = create<UIState>((set) => ({
  sidebarOpen: true,
  theme: 'light',
  toggleSidebar: () => set((state) => ({
    sidebarOpen: !state.sidebarOpen
  })),
  setTheme: (theme) => set({ theme }),
}));
```

### 移动端架构
**跨端策略**：
```javascript
// Taro配置 (微信生态)
export default {
  projectName: 'luhao-esign-mini',
  date: '2025-6-22',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: 'dist',
  plugins: [
    '@tarojs/plugin-html'
  ],
  defineConstants: {
    API_BASE_URL: JSON.stringify('https://api.example.com')
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      }
    }
  }
};

// UniApp配置 (多平台)
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "路浩电子签"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "路浩电子签",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "condition": {
    "current": 0,
    "list": []
  }
}
```

## Kubernetes部署架构

### 命名空间规划
```yaml
# 环境隔离
apiVersion: v1
kind: Namespace
metadata:
  name: luhao-esign-prod
  labels:
    env: production
    app: luhao-esign
---
apiVersion: v1
kind: Namespace
metadata:
  name: luhao-esign-test
  labels:
    env: testing
    app: luhao-esign
---
apiVersion: v1
kind: Namespace
metadata:
  name: luhao-esign-dev
  labels:
    env: development
    app: luhao-esign
```

### 工作负载设计
```yaml
# Java微服务部署示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: contract-service
  namespace: luhao-esign-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: contract-service
  template:
    metadata:
      labels:
        app: contract-service
        version: v1
    spec:
      containers:
      - name: contract-service
        image: luhao/contract-service:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
# Go微服务部署示例
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-service
  namespace: luhao-esign-prod
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-service
  template:
    metadata:
      labels:
        app: ai-service
        version: v1
    spec:
      containers:
      - name: ai-service
        image: luhao/ai-service:v1.0.0
        ports:
        - containerPort: 9000
        env:
        - name: ENV
          value: "production"
        - name: REGISTRY_ADDR
          value: "nacos-service:8848"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
```

### 服务发现配置
```yaml
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: contract-service
  namespace: luhao-esign-prod
spec:
  selector:
    app: contract-service
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP
---
# Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: luhao-esign-ingress
  namespace: luhao-esign-prod
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.luhao-esign.com
    secretName: luhao-esign-tls
  rules:
  - host: api.luhao-esign.com
    http:
      paths:
      - path: /api/contract
        pathType: Prefix
        backend:
          service:
            name: contract-service
            port:
              number: 8080
      - path: /api/ai
        pathType: Prefix
        backend:
          service:
            name: ai-service
            port:
              number: 9000
```

### 存储设计
```yaml
# PostgreSQL StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql
  namespace: luhao-esign-prod
spec:
  serviceName: postgresql
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      containers:
      - name: postgresql
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: "luhao_esign"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi
```

### 配置管理
```yaml
# ConfigMap配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: luhao-esign-prod
data:
  application.yml: |
    spring:
      datasource:
        url: *********************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      redis:
        host: redis-service
        port: 6379

    nacos:
      server-addr: nacos-service:8848
      namespace: production
---
# Secret配置
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
  namespace: luhao-esign-prod
type: Opaque
data:
  db-username: bHVoYW8= # base64编码
  db-password: cGFzc3dvcmQ= # base64编码
  jwt-secret: and0LXNlY3JldC1rZXk= # base64编码
```

### 自动扩缩容
```yaml
# HPA配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: contract-service-hpa
  namespace: luhao-esign-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: contract-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

## 监控运维体系

### 链路追踪：Zipkin
```yaml
# Zipkin部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zipkin
  namespace: luhao-esign-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zipkin
  template:
    metadata:
      labels:
        app: zipkin
    spec:
      containers:
      - name: zipkin
        image: openzipkin/zipkin:latest
        ports:
        - containerPort: 9411
        env:
        - name: STORAGE_TYPE
          value: "elasticsearch"
        - name: ES_HOSTS
          value: "elasticsearch:9200"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### 指标监控：Prometheus + Grafana
```yaml
# Prometheus配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: luhao-esign-prod
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    scrape_configs:
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)

    - job_name: 'contract-service'
      static_configs:
      - targets: ['contract-service:8080']
      metrics_path: '/actuator/prometheus'

    alerting:
      alertmanagers:
      - static_configs:
        - targets: ['alertmanager:9093']
---
# 告警规则
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: luhao-esign-prod
data:
  alert_rules.yml: |
    groups:
    - name: luhao-esign-alerts
      rules:
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"

      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "Service {{ $labels.instance }} is down"
```

### 日志系统：ELK + SLS
```yaml
# Filebeat配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: filebeat-config
  namespace: luhao-esign-prod
data:
  filebeat.yml: |
    filebeat.inputs:
    - type: container
      paths:
        - /var/log/containers/*luhao-esign*.log
      processors:
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
          - logs_path:
              logs_path: "/var/log/containers/"

    output.elasticsearch:
      hosts: ["elasticsearch:9200"]
      index: "luhao-esign-logs-%{+yyyy.MM.dd}"

    setup.template.name: "luhao-esign"
    setup.template.pattern: "luhao-esign-*"

    logging.level: info
    logging.to_files: true
    logging.files:
      path: /var/log/filebeat
      name: filebeat
      keepfiles: 7
      permissions: 0644
```

## 非功能需求

### 1. 性能要求

**性能指标预估**：
- **QPS预估**：
  - 核心接口（合同签署）：1,000 QPS
  - 一般接口（查询类）：5,000 QPS
  - AI服务接口：100 QPS
- **响应时间目标**：
  - P95响应时间 < 500ms
  - P99响应时间 < 1000ms
- **并发用户数**：在线用户峰值 10,000
- **吞吐量要求**：日处理合同数量 1万份

**性能优化策略**：
```yaml
# 缓存优化
缓存架构:
  L1_本地缓存: # Caffeine
    - 用户权限: TTL 5分钟
    - 系统配置: TTL 30分钟
  L2_Redis缓存:
    - 用户会话: TTL 2小时
    - 热点数据: TTL 1天
  L3_CDN缓存:
    - 静态资源: TTL 7天
    - 合同预览: TTL 1小时

# 异步处理
异步任务:
  - 合同生成: RabbitMQ队列
  - 文件转换: 后台任务
  - 通知发送: 异步队列
  - 数据统计: 定时任务

# 数据库优化
数据库优化:
  - 读写分离: 主库写入，从库读取
  - 分库分表: 按企业ID分片
  - 索引优化: 覆盖索引，复合索引
  - 连接池: HikariCP优化配置
```

### 2. 安全要求

**认证授权方案**：
```java
// JWT配置
@Configuration
public class JwtConfig {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("authorities", userDetails.getAuthorities());
        return createToken(claims, userDetails.getUsername());
    }

    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }
}

// RBAC权限控制
@PreAuthorize("hasRole('ADMIN') or hasPermission(#contractId, 'CONTRACT', 'READ')")
public ContractDTO getContract(@PathVariable Long contractId) {
    return contractService.getContract(contractId);
}
```

**数据安全防护**：
```java
// 数据加密工具类
@Component
public class EncryptionUtil {

    @Value("${encryption.key}")
    private String encryptionKey;

    // AES-256加密
    public String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(
                encryptionKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            byte[] encrypted = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    // 敏感字段加密存储
    @PrePersist
    @PreUpdate
    public void encryptSensitiveFields(Object entity) {
        if (entity instanceof User) {
            User user = (User) entity;
            if (user.getIdCard() != null) {
                user.setIdCard(encrypt(user.getIdCard()));
            }
            if (user.getMobile() != null) {
                user.setMobile(encrypt(user.getMobile()));
            }
        }
    }
}

// SQL注入防护
@Repository
public class ContractRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 使用参数化查询
    public List<Contract> findByStatus(Integer status) {
        String sql = "SELECT * FROM contracts WHERE status = ?";
        return jdbcTemplate.query(sql, new Object[]{status},
            new ContractRowMapper());
    }
}
```

### 3. 可扩展性设计

**水平扩展策略**：
```yaml
# 服务扩展配置
服务扩展:
  微服务扩展:
    - 无状态设计: 所有服务无状态
    - 负载均衡: Nginx + K8s Service
    - 自动扩缩容: HPA基于CPU/内存

  数据库扩展:
    - 读写分离: 1主2从配置
    - 分库分表: 按企业ID水平分片
    - 连接池: 动态调整连接数

  缓存扩展:
    - Redis集群: 3主3从配置
    - 一致性哈希: 数据分片均匀
    - 故障转移: 自动主从切换

# 容量规划
容量规划:
  业务增长预测:
    - 用户增长: 年增长率200%
    - 数据增长: 年增长率300%
    - 流量增长: 年增长率250%

  资源规划:
    - 服务器资源: 预留50%冗余
    - 存储资源: 预留100%冗余
    - 网络带宽: 预留30%冗余
```

### 4. 高可用性设计

**可用性目标**：
- **SLA指标**：99.9%可用性（年停机时间 < 8.76小时）
- **故障恢复时间**：RTO < 15分钟，RPO < 5分钟

**冗余部署策略**：
```yaml
# 高可用部署
高可用配置:
  服务冗余:
    - 多实例部署: 每个服务至少2个实例
    - 跨AZ部署: 分布在不同可用区
    - 健康检查: 自动故障检测和恢复

  数据库冗余:
    - 主从复制: 1主2从异步复制
    - 自动故障转移: 10分钟完成切换
    - 数据备份: 每日全量+实时增量

  负载均衡:
    - 多层负载均衡: SLB + Nginx + K8s
    - 健康检查: 多维度健康检测
    - 故障隔离: 自动摘除故障节点

# 容灾方案
容灾方案:
  同城容灾:
    - 双机房部署: 主备机房实时同步
    - 数据同步: 5分钟内数据同步
    - 切换时间: 60分钟内完成切换

  异地容灾:
    - 异地备份: 每日数据备份到异地
    - 灾备中心: 异地灾备环境
    - 恢复时间: 4小时内恢复服务
```

### 5. 分布式系统设计

**分布式系统原理**：
```yaml
# CAP定理权衡
CAP权衡:
  一致性(Consistency):
    - 强一致性: 关键业务数据（合同、签署记录）
    - 最终一致性: 统计数据、日志数据

  可用性(Availability):
    - 优先保证可用性: 查询类服务
    - 适当牺牲可用性: 写入类服务

  分区容错性(Partition Tolerance):
    - 网络分区处理: 自动重试机制
    - 数据分片: 按业务维度分片

# BASE理论实践
BASE实践:
  基本可用(Basically Available):
    - 服务降级: 非核心功能降级
    - 限流熔断: 保护核心服务

  软状态(Soft State):
    - 中间状态: 签署中、审批中等状态
    - 状态同步: 异步状态同步机制

  最终一致性(Eventually Consistent):
    - 数据同步: 最终数据一致
    - 补偿机制: 数据不一致时的补偿
```

**数据安全备份**：
```bash
#!/bin/bash
# 数据备份脚本

# PostgreSQL备份
pg_dump -h $PG_HOST -U $PG_USER -d luhao_esign \
  --format=custom --compress=9 \
  --file=/backup/postgres/luhao_esign_$(date +%Y%m%d_%H%M%S).dump

# MongoDB备份
mongodump --host $MONGO_HOST --db luhao_esign_logs \
  --gzip --archive=/backup/mongodb/logs_$(date +%Y%m%d_%H%M%S).gz

# Redis备份
redis-cli --rdb /backup/redis/dump_$(date +%Y%m%d_%H%M%S).rdb

# 文件备份到异地
rsync -avz /backup/ backup-server:/remote/backup/

# 备份验证
backup_verify() {
    local backup_file=$1
    if [ -f "$backup_file" ]; then
        echo "Backup successful: $backup_file"
        # 发送成功通知
        curl -X POST "$WEBHOOK_URL" -d "Backup completed: $backup_file"
    else
        echo "Backup failed: $backup_file"
        # 发送失败告警
        curl -X POST "$ALERT_URL" -d "Backup failed: $backup_file"
    fi
}
```

**证据链存证设计**：
```java
// 存证服务实现
@Service
public class EvidenceService {

    @Autowired
    private BlockchainClient blockchainClient;

    @Autowired
    private TimestampService timestampService;

    // 合同存证
    public EvidenceChain createEvidence(Contract contract) {
        // 1. 计算合同哈希
        String contractHash = calculateHash(contract);

        // 2. 获取可信时间戳
        Timestamp timestamp = timestampService.getTimestamp(contractHash);

        // 3. 构建存证数据
        EvidenceData evidenceData = EvidenceData.builder()
            .contractId(contract.getId())
            .contractHash(contractHash)
            .timestamp(timestamp)
            .signers(contract.getSigners())
            .build();

        // 4. 上链存证
        String txHash = blockchainClient.submitEvidence(evidenceData);

        // 5. 保存存证记录
        EvidenceChain evidence = new EvidenceChain();
        evidence.setContractId(contract.getId());
        evidence.setEvidenceHash(contractHash);
        evidence.setBlockchainTxHash(txHash);
        evidence.setTimestamp(timestamp.getTime());
        evidence.setStatus(EvidenceStatus.CONFIRMED);

        return evidenceRepository.save(evidence);
    }

    // 存证验证
    public boolean verifyEvidence(Long contractId) {
        EvidenceChain evidence = evidenceRepository.findByContractId(contractId);
        if (evidence == null) {
            return false;
        }

        // 验证区块链记录
        boolean blockchainValid = blockchainClient.verifyTransaction(
            evidence.getBlockchainTxHash());

        // 验证时间戳
        boolean timestampValid = timestampService.verifyTimestamp(
            evidence.getTimestamp(), evidence.getEvidenceHash());

        return blockchainValid && timestampValid;
    }

    private String calculateHash(Contract contract) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String data = contract.getId() + contract.getContent() +
                         contract.getSigners().toString();
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Hash calculation failed", e);
        }
    }
}
```

**分布式事务**：
```java
// TCC分布式事务示例
@Service
public class ContractSigningService {

    @Autowired
    private ContractService contractService;

    @Autowired
    private SealService sealService;

    @Autowired
    private NotificationService notificationService;

    // 合同签署分布式事务
    @GlobalTransactional
    public void signContract(Long contractId, Long userId, Long sealId) {
        try {
            // Try阶段
            contractService.trySign(contractId, userId);
            sealService.tryUseSeal(sealId, userId);
            notificationService.tryNotify(contractId, userId);

            // Confirm阶段
            contractService.confirmSign(contractId, userId);
            sealService.confirmUseSeal(sealId, userId);
            notificationService.confirmNotify(contractId, userId);

        } catch (Exception e) {
            // Cancel阶段
            contractService.cancelSign(contractId, userId);
            sealService.cancelUseSeal(sealId, userId);
            notificationService.cancelNotify(contractId, userId);
            throw e;
        }
    }
}

// Saga分布式事务示例
@SagaOrchestrationStart
public class ContractWorkflowSaga {

    @SagaOrchestrationTask
    public void createContract(ContractCreateEvent event) {
        // 创建合同
        contractService.createContract(event.getContractData());
    }

    @SagaOrchestrationTask
    public void submitApproval(ContractCreatedEvent event) {
        // 提交审批
        approvalService.submitApproval(event.getContractId());
    }

    @SagaOrchestrationTask
    public void sendNotification(ApprovalCompletedEvent event) {
        // 发送通知
        notificationService.sendApprovalNotification(event.getContractId());
    }

    // 补偿操作
    @SagaOrchestrationTask
    public void compensateCreateContract(ContractCreateEvent event) {
        contractService.deleteContract(event.getContractId());
    }
}
```

## 核心技术实现思路

### 1. 中间件与可观测性

平台采用成熟稳定的中间件技术栈，构建完整的可观测性体系，确保系统的高可用性和可维护性。

| 类型 | 技术选型 | 用途 | 选型理由 |
| :--- | :--- | :--- | :--- |
| **消息队列** | **RabbitMQ** | 服务间的异步通信、任务解耦、流量削峰。例如，合同签署完成后的多渠道通知、异步生成报表等。 | 成熟稳定，支持多种消息模式（如Fanout, Direct, Topic），并有完善的延迟队列和死信队列机制，足以满足当前业务需求。 |
| **监控告警** | **Prometheus + Grafana** | 采集和存储所有微服务的性能指标（Metrics），通过Grafana进行可视化展示和配置告警规则。 | 云原生领域的事实标准，与K8s生态无缝集成，便于对服务进行全方位的性能监控。 |
| **日志系统** | **ELK Stack (Elasticsearch, Logstash, Kibana)** | 集中收集、存储和分析所有微服务的应用日志。Kibana提供强大的日志查询和可视化界面。 | 成熟的日志解决方案，便于开发者进行问题排查和系统审计。 |

### 2. 分布式存储架构设计

针对平台中海量的非结构化文件（合同、附件、印章图片等），我们设计了统一的分布式存储方案，其核心目标是高可用、高持久、安全可控。

#### 2.1 统一上传流程设计

**流程说明**：
1. 前端向业务服务（如`contract-service`）请求上传凭证
2. 业务服务生成一个预签名的URL（Pre-signed URL），该URL具有时效性（如5分钟）和特定的上传权限，并包含文件最终在对象存储中的路径（Object Key）。路径命名规范：`{tenant_id}/{business_type}/{date}/{uuid}.{ext}`
3. 前端使用该URL，通过HTTP PUT请求直接将文件流上传至对象存储（MinIO/S3），绕过业务服务器，避免不必要的带宽和内存消耗
4. 上传成功后，前端将Object Key和文件元数据（文件名、大小、Hash等）提交给业务服务
5. 业务服务将文件元数据与其业务数据（如合同ID）在数据库中进行关联

**技术实现流程图**：
```mermaid
sequenceDiagram
    participant F as 前端
    participant BS as 业务服务
    participant MinIO as 对象存储
    participant DB as 数据库

    F->>BS: 1. 请求上传凭证
    BS->>BS: 2. 生成预签名URL
    BS-->>F: 3. 返回预签名URL
    F->>MinIO: 4. 直接上传文件
    MinIO-->>F: 5. 上传成功
    F->>BS: 6. 提交文件元数据
    BS->>DB: 7. 保存文件关联信息
```

**实现细节**：
1. **预签名URL机制**：业务服务生成具有时效性（5分钟）的预签名URL，包含特定上传权限
2. **路径命名规范**：`{tenant_id}/{business_type}/{date}/{uuid}.{ext}`，确保文件唯一性和可追溯性
3. **直接上传**：前端通过HTTP PUT请求直接上传至对象存储，避免业务服务器带宽消耗
4. **元数据关联**：上传成功后，将Object Key和文件元数据与业务数据进行关联

#### 2.2 访问控制与安全策略

**安全设计原则**：
- 所有存储桶（Bucket）均设置为**私有读写**
- 外部用户或前端应用绝不直接通过永久密钥访问，所有访问（上传/下载）均通过上述有时效性的预签名URL进行，实现最小权限和租户隔离
- 启用服务端加密（SSE-S3），由对象存储服务自动对写入的文件进行加密，进一步增强数据安全性
```yaml
安全策略:
  存储桶配置:
    - 所有桶设置为私有读写
    - 禁止直接通过永久密钥访问
    - 启用服务端加密(SSE-S3)

  访问控制:
    - 预签名URL临时授权
    - 最小权限原则
    - 租户数据隔离

  加密策略:
    - 传输加密: HTTPS/TLS 1.3
    - 存储加密: AES-256
    - 密钥管理: 独立KMS服务
```

#### 2.3 高可用与持久性保障

**高可用架构设计**：
- **私有化部署(MinIO)**：采用纠删码（Erasure Coding）模式部署，例如`EC:4`表示数据被分成4个数据块和4个校验块，存储在8台不同的服务器上。这种模式允许最多4台服务器宕机而不丢失数据，极大地提高了存储的利用率和可靠性
- **公有云部署(OSS)**：直接利用云厂商提供的多副本、跨可用区（AZ）存储能力，数据持久性可达99.9999999999%（12个9），免去自行维护的复杂性

**合同分布式存储逻辑**：
- **存储方案**：采用**MinIO**对象存储集群，进行私有化部署，确保数据物理安全。集群采用纠删码模式，实现高可用和数据冗余
- **存储逻辑**：
  1. **原文上传**：用户上传的原始文件（Word/图片等）存入`raw-files`桶
  2. **PDF转换**：系统统一转换为PDF后，存入`pdf-preview`桶，用于签署过程中的预览
  3. **版本化存储**：每次签署操作后，生成的带有新数字签名的PDF文件，作为一个**新版本**存入`signed-contracts`桶，利用MinIO的版本控制功能保留所有签署过程中的文件版本，便于追溯。最终完成的合同是该对象的最新版本
  4. **证据报告**：生成的证据链报告PDF，存入`evidence-reports`桶
- **访问控制**：所有桶均设置为私有。业务服务通过生成的临时授权URL（presigned URL）访问文件，避免AK/SK在网络中传输

#### 2.4 分桶存储策略
```yaml
存储桶设计:
  raw-files: # 原始文件桶
    - 用户上传的Word/图片等原始文件
    - 保留期: 永久
    - 访问频率: 低

  pdf-preview: # PDF预览桶
    - 系统转换后的PDF文件
    - 用于签署过程预览
    - 访问频率: 高

  signed-contracts: # 已签署合同桶
    - 带数字签名的最终PDF
    - 版本化存储
    - 法律效力文件

  evidence-reports: # 证据报告桶
    - 证据链报告PDF
    - 司法鉴定文件
    - 长期保存
```

### 3. 安全加密与防篡改体系

安全是电子签平台的生命线。我们从数据、传输、存储、合规等多个维度构建纵深防御体系。

#### 3.1 合同唯一性与防篡改机制

**核心安全设计原则**：
- **合同唯一ID**：每份合同（每条签署流程）在创建时，系统会生成一个全局唯一的、趋势递增的ID（如使用雪花算法`Snowflake`）。此ID将贯穿合同的整个生命周期
- **文件摘要算法**：所有上传的文件在进入签署流程前，都会使用国密**`SM3`**算法（或**`SHA-256`**作为备选）计算其内容的哈希摘要。此摘要将作为文件的唯一"数字指纹"
- **数字签名**：每一方签署时，平台会调用CA（证书颁发机构）服务，使用签署人的个人/企业数字证书，对**当前文件版本的内容摘要**和**关键签署信息**（如签署人身份、时间、IP）进行数字签名
- **时间戳**：每次签名操作都会加盖一个由权威TSA（时间戳服务机构）颁发的、具有法律效力的可信时间戳，精确记录签署发生的法定时间
- **防篡改机制**：最终生成的合同PDF，会将所有签署方的数字签名、时间戳、证据链信息嵌入其中。任何对PDF内容的微小改动都会导致至少一个数字签名失效，通过合同验签功能即可立即识别
```java
// 合同唯一ID生成（雪花算法）
@Component
public class ContractIdGenerator {
    private final SnowflakeIdWorker idWorker;

    public Long generateContractId() {
        return idWorker.nextId();
    }
}

// 文件摘要计算
@Service
public class FileHashService {

    public String calculateFileHash(InputStream fileStream) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SM3");
            // 国密SM3算法计算文件哈希
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fileStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
            return Hex.encodeHexString(digest.digest());
        } catch (Exception e) {
            throw new RuntimeException("文件哈希计算失败", e);
        }
    }
}
```

**技术实现代码**：
```java
// 数字签名服务
@Service
public class DigitalSignatureService {

    @Autowired
    private CAService caService;

    @Autowired
    private TimestampService timestampService;

    public SignatureResult signContract(ContractSignRequest request) {
        // 1. 计算合同内容摘要
        String contentHash = calculateContentHash(request.getContractContent());

        // 2. 构建签署信息
        SignatureData signData = SignatureData.builder()
            .contentHash(contentHash)
            .signerId(request.getSignerId())
            .signerInfo(request.getSignerInfo())
            .signTime(Instant.now())
            .ipAddress(request.getIpAddress())
            .build();

        // 3. 调用CA服务进行数字签名
        DigitalSignature signature = caService.sign(signData, request.getCertificate());

        // 4. 获取可信时间戳
        Timestamp timestamp = timestampService.getTimestamp(contentHash);

        // 5. 构建签署结果
        return SignatureResult.builder()
            .signature(signature)
            .timestamp(timestamp)
            .signatureHash(calculateSignatureHash(signature))
            .build();
    }
}
```

#### 3.2 数据加密方案

**传输与存储加密策略**：
- **传输加密**：客户端与服务器之间的所有通信，均强制使用**HTTPS (TLS 1.3)**协议进行加密，防止数据在传输过程中被窃听或篡改
- **存储加密(静态数据加密)**：
  - **文件加密**：所有存储在对象存储（MinIO/OSS）中的合同、附件等敏感文件，均使用**AES-256**对称加密算法进行加密存储。每个文件使用独立的密钥（DEK）
  - **密钥管理**：文件加密密钥（DEK）本身，则使用**RSA-2048**非对称加密或通过KMS（密钥管理服务）的主密钥进行加密保护。这确保了即使存储介质泄露，文件内容也无法被解密
  - **数据库加密**：核心敏感字段（如用户身份证号、手机号）在数据库中采用加密或脱敏方式存储

**安全加密核心实现**：
- **传输层安全**：全链路强制TLS 1.3，API Gateway终结TLS，内部服务间通信启用mTLS
- **数据存储加密**：
  - **敏感信息**：用户身份证号、手机号、银行卡号等在数据库中**必须**使用`AES-256-GCM`或国密`SM4`算法加密存储，密钥由独立的KMS（密钥管理服务）管理
  - **合同文件**：存储在MinIO对象存储中的合同文件，采用服务端加密（SSE-S3），由MinIO管理加密密钥
- **电子签名核心算法**：
  - **摘要算法**：对合同原文PDF计算摘要，必须使用`SHA-256`或国密`SM3`
  - **签名算法**：调用CA机构服务时，使用`RSA-2048`或国密`SM2`非对称加密算法生成数字签名
  - **时间戳**：采用`RFC3161`标准协议与可信时间戳中心交互

#### 3.3 防篡改机制
```yaml
防篡改策略:
  数字签名嵌入:
    - 将数字签名嵌入PDF文件
    - 包含签署人信息、时间戳、证书链
    - 任何内容修改都会导致签名失效

  完整性校验:
    - 文件哈希值验证
    - 数字签名验证
    - 时间戳验证
    - 证书链验证

  版本控制:
    - 每次签署生成新版本
    - 保留所有历史版本
    - 版本间差异追踪
```

### 4. AI模块技术实现策略

AI是本平台的核心差异化能力。我们将自研与第三方服务相结合，构建强大的AI赋能域。

#### 4.1 核心AI能力栈

**技术选型与架构**：
- **大语言模型(LLM)**：支持灵活接入和切换多种业界领先的大模型，如**Deepseek-V2**, **Qwen-Max**, **ChatGLM-4**, **豆包Pro**等。通过统一的接口层进行封装，方便进行模型评估和路由
- **向量数据库**：使用**Milvus**或**PostgreSQL (pgvector)**来存储合同条款、法律法规、企业知识库等文本的向量化表示，是实现RAG（检索增强生成）的核心
- **AI计算框架**：使用**Python (FastAPI/PyTorch)**来构建AI服务，提供模型推理接口
```yaml
AI技术栈:
  大语言模型:
    - 主模型: Qwen-Max, ChatGLM-4
    - 备用模型: Deepseek-V2, 豆包Pro
    - 模型路由: 智能负载均衡

  向量数据库:
    - 主选: Milvus (高性能向量检索)
    - 备选: PostgreSQL + pgvector
    - 索引算法: HNSW, IVF

  AI框架:
    - 推理服务: Python + FastAPI
    - 模型管理: PyTorch + Transformers
    - 向量化: BGE, Sentence-BERT
```

#### 4.2 AI功能实现方案

**合同智能生成与审查(RAG)**：
1. **知识库构建**：将高质量的合同范本、法律法规、企业自己的标准合同等进行切片、向量化，存入向量数据库，构建成专业知识库
2. **用户意图理解**：当用户通过对话或上传合同进行交互时，首先用LLM理解其核心需求
3. **检索增强**：将用户需求或待审查的合同文本转换为向量，在知识库中检索最相关的条款或风险点作为上下文（Context）
4. **增强生成**：将原始请求和检索到的上下文一起打包成一个结构化的Prompt，发送给LLM，生成更精准、更专业的合同文本或审查报告

**印章OCR识别与智能抠图**：
1. **模型选型**：使用**U-Net**或类似的图像分割模型，结合传统的计算机视觉技术（如霍夫圆变换、边缘检测）进行印章识别和定位
2. **数据增强**：在训练模型时，使用大量不同光照、角度、模糊程度的印章图片进行数据增强，提升模型鲁棒性
3. **后处理**：对模型输出的掩码（Mask）进行精细化处理，去除毛刺和背景噪声，生成高保真的、带透明通道的PNG图片

**AI合同生成与抠图技术实现**：
- **AI合同生成**：
  - **技术栈**：LangChain / LlamaIndex + 私有化部署的大模型（如ChatGLM, Qwen） + Milvus向量数据库
  - **流程**：
    1. **知识库构建**：将海量法律法规、标准合同范本、企业自有合同等进行切分、清洗，通过Embedding模型（如BGE）向量化后存入Milvus
    2. **意图识别**：`ai-generation-service`识别用户意图（如"生成一份租赁合同"）
    3. **RAG检索**：根据用户意图和对话内容，在向量数据库中检索最相关的法律条款和合同片段
    4. **Prompt构建**：将用户需求、检索到的知识、预设的Prompt模板组合成一个丰富的Prompt
    5. **LLM调用**：调用大模型服务，生成合同文本
    6. **后处理**：对生成内容进行校验、格式化，并返回给用户
- **电子章智能抠图**：
  - **技术栈**：OpenCV, PaddleSeg/U-Net
  - **流程**：
    1. **图像预处理**：`ai-ocr-service`对上传的印章图片进行尺寸归一化、去噪、二值化
    2. **印章定位**：使用基于深度学习的图像分割模型，精确定位印章的主体像素区域
    3. **背景去除**：将分割出的印章区域外的所有像素设置为透明
    4. **边缘优化**：使用形态学操作（如腐蚀、膨胀）平滑印章边缘，去除毛刺
    5. **颜色标准化**：将印章颜色统一为标准的"中国红"

**技术实现代码示例**：
```python
# RAG合同生成服务
class ContractGenerationService:

    def __init__(self):
        self.vector_db = MilvusClient()
        self.llm_client = LLMClient()
        self.embedding_model = EmbeddingModel()

    async def generate_contract(self, user_request: str) -> str:
        # 1. 用户意图理解
        intent = await self.understand_intent(user_request)

        # 2. 知识库检索
        query_vector = self.embedding_model.encode(user_request)
        relevant_docs = self.vector_db.search(
            collection_name="contract_knowledge",
            query_vectors=[query_vector],
            limit=10,
            metric_type="COSINE"
        )

        # 3. 构建增强Prompt
        context = self.build_context(relevant_docs)
        prompt = self.build_prompt(user_request, context, intent)

        # 4. LLM生成
        response = await self.llm_client.generate(
            prompt=prompt,
            max_tokens=4000,
            temperature=0.7
        )

        # 5. 后处理与格式化
        contract = self.post_process(response)

        return contract

    def build_context(self, docs: List[Document]) -> str:
        """构建检索到的知识上下文"""
        context_parts = []
        for doc in docs:
            context_parts.append(f"参考条款: {doc.content}")
        return "\n".join(context_parts)
```

#### 4.3 智能合同审查
```python
# 合同审查服务
class ContractReviewService:

    def __init__(self):
        self.risk_detector = RiskDetectionModel()
        self.compliance_checker = ComplianceChecker()
        self.llm_client = LLMClient()

    async def review_contract(self, contract_content: str) -> ReviewResult:
        # 1. 风险点识别
        risk_points = await self.detect_risks(contract_content)

        # 2. 合规性检查
        compliance_issues = await self.check_compliance(contract_content)

        # 3. 条款完整性检查
        missing_clauses = await self.check_completeness(contract_content)

        # 4. 生成审查报告
        review_report = await self.generate_review_report(
            contract_content, risk_points, compliance_issues, missing_clauses
        )

        return ReviewResult(
            risk_level=self.calculate_risk_level(risk_points),
            risk_points=risk_points,
            compliance_issues=compliance_issues,
            missing_clauses=missing_clauses,
            suggestions=review_report.suggestions,
            review_report=review_report.content
        )
```

#### 4.4 印章OCR与智能抠图
```python
# 印章处理服务
class SealProcessingService:

    def __init__(self):
        self.segmentation_model = UNetModel()
        self.ocr_model = PaddleOCR()

    async def process_seal_image(self, image_data: bytes) -> SealResult:
        # 1. 图像预处理
        image = self.preprocess_image(image_data)

        # 2. 印章定位与分割
        mask = self.segmentation_model.predict(image)
        seal_region = self.extract_seal_region(image, mask)

        # 3. 背景去除
        transparent_seal = self.remove_background(seal_region, mask)

        # 4. 边缘优化
        optimized_seal = self.optimize_edges(transparent_seal)

        # 5. 颜色标准化
        standardized_seal = self.standardize_color(optimized_seal)

        # 6. OCR文字识别
        seal_text = self.ocr_model.recognize(seal_region)

        return SealResult(
            processed_image=standardized_seal,
            seal_text=seal_text,
            confidence=self.calculate_confidence(mask),
            seal_type=self.classify_seal_type(seal_text)
        )

    def preprocess_image(self, image_data: bytes) -> np.ndarray:
        """图像预处理：尺寸归一化、去噪、二值化"""
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)

        # 尺寸归一化
        image = cv2.resize(image, (512, 512))

        # 去噪
        image = cv2.bilateralFilter(image, 9, 75, 75)

        # 二值化
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return image
```

### 5. 消息队列与事件驱动架构

#### 5.1 RabbitMQ消息流转设计
```yaml
# 消息交换机设计
exchanges:
  contract_exchange:
    type: topic
    description: "合同状态变更事件"
    routing_keys:
      - "contract.created"      # 合同创建
      - "contract.signed"       # 合同签署
      - "contract.completed"    # 合同完成
      - "contract.rejected"     # 合同拒签
      - "contract.expired"      # 合同过期

  seal_exchange:
    type: direct
    description: "印章使用审批事件"
    routing_keys:
      - "seal.approval.request"   # 用印申请
      - "seal.approval.approved"  # 审批通过
      - "seal.approval.rejected"  # 审批拒绝

  ai_exchange:
    type: direct
    description: "AI任务处理事件"
    routing_keys:
      - "ai.contract.generate"    # 合同生成
      - "ai.contract.review"      # 合同审查
      - "ai.ocr.extract"          # OCR识别

# 队列消费者设计
consumers:
  notification_service:
    queues: ["notification_queue"]
    routing_keys: ["contract.*", "seal.approval.*"]
    description: "处理所有通知消息"

  webhook_service:
    queues: ["webhook_queue"]
    routing_keys: ["contract.completed", "contract.rejected"]
    description: "处理外部回调"

  statistics_service:
    queues: ["statistics_queue"]
    routing_keys: ["contract.*", "seal.*"]
    description: "处理统计分析"
```

#### 5.2 事件驱动架构实现
```java
// 事件发布者
@Component
public class ContractEventPublisher {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void publishContractSigned(ContractSignedEvent event) {
        rabbitTemplate.convertAndSend(
            "contract_exchange",
            "contract.signed",
            event
        );
    }

    public void publishContractCompleted(ContractCompletedEvent event) {
        rabbitTemplate.convertAndSend(
            "contract_exchange",
            "contract.completed",
            event
        );
    }
}

// 事件消费者
@RabbitListener(queues = "notification_queue")
@Component
public class NotificationEventConsumer {

    @Autowired
    private NotificationService notificationService;

    @RabbitHandler
    public void handleContractSigned(ContractSignedEvent event) {
        // 发送签署通知
        notificationService.sendSignedNotification(event);
    }

    @RabbitHandler
    public void handleContractCompleted(ContractCompletedEvent event) {
        // 发送完成通知
        notificationService.sendCompletedNotification(event);
    }
}
```

### 6. 监控与可观测性体系

#### 6.1 全链路监控架构
```yaml
监控体系:
  指标监控(Metrics):
    - Prometheus: 指标采集存储
    - Grafana: 可视化展示
    - AlertManager: 告警管理

  链路追踪(Tracing):
    - Zipkin: 分布式链路追踪
    - Jaeger: 链路分析
    - OpenTelemetry: 统一遥测

  日志监控(Logging):
    - ELK Stack: 日志收集分析
    - Filebeat: 日志采集
    - Logstash: 日志处理
    - Kibana: 日志查询可视化

  业务监控:
    - 合同签署成功率
    - AI服务响应时间
    - 用户活跃度指标
    - 系统错误率统计
```

#### 6.2 关键监控指标
```yaml
# 业务指标
business_metrics:
  contract_metrics:
    - contract_creation_rate: "合同创建速率"
    - contract_completion_rate: "合同完成率"
    - contract_rejection_rate: "合同拒签率"
    - average_signing_duration: "平均签署时长"

  ai_metrics:
    - ai_generation_success_rate: "AI生成成功率"
    - ai_review_accuracy: "AI审查准确率"
    - ocr_recognition_accuracy: "OCR识别准确率"
    - ai_response_time: "AI服务响应时间"

# 技术指标
technical_metrics:
  system_metrics:
    - cpu_usage_percent: "CPU使用率"
    - memory_usage_percent: "内存使用率"
    - disk_usage_percent: "磁盘使用率"
    - network_io_rate: "网络IO速率"

  application_metrics:
    - request_rate: "请求速率(QPS)"
    - response_time_p95: "95%响应时间"
    - error_rate: "错误率"
    - active_connections: "活跃连接数"
```

## 交付物要求

### 1. 架构图

**微服务拆分图**：

```mermaid
graph TB
    subgraph "用户域"
        US[用户服务<br/>user-service]
        ES[企业服务<br/>enterprise-service]
        OS[组织服务<br/>org-service]
    end

    subgraph "合同域"
        CS[合同服务<br/>contract-service]
        TS[模板服务<br/>template-service]
        SS[印章服务<br/>seal-service]
        WS[工作流服务<br/>workflow-service]
    end

    subgraph "AI域"
        AIS[AI服务<br/>ai-service]
        OCRS[OCR服务<br/>ocr-service]
    end

    subgraph "支撑域"
        FS[文件服务<br/>file-service]
        NS[通知服务<br/>notification-service]
        BS[计费服务<br/>billing-service]
        EVS[存证服务<br/>evidence-service]
    end

    US --> OS
    ES --> OS
    CS --> US
    CS --> ES
    CS --> SS
    CS --> WS
    CS --> FS
    SS --> OS
    WS --> NS
    CS --> AIS
    AIS --> OCRS
    CS --> EVS
    BS --> US
    BS --> ES
```

**数据流向图**：

```mermaid
sequenceDiagram
    participant U as 用户
    participant GW as API网关
    participant CS as 合同服务
    participant AIS as AI服务
    participant SS as 印章服务
    participant EVS as 存证服务
    participant MQ as 消息队列
    participant NS as 通知服务

    U->>GW: 1. 发起合同签署
    GW->>CS: 2. 路由到合同服务
    CS->>AIS: 3. AI审查合同
    AIS-->>CS: 4. 返回审查结果
    CS->>SS: 5. 申请印章使用
    SS-->>CS: 6. 印章使用授权
    CS->>CS: 7. 更新合同状态
    CS->>MQ: 8. 发送状态变更消息
    MQ->>NS: 9. 通知服务消费消息
    NS->>U: 10. 发送签署通知
    CS->>EVS: 11. 生成存证
    EVS-->>CS: 12. 存证完成
```

### 2. 数据库设计文档

**完整DDL语句**：
```sql
-- 创建数据库
CREATE DATABASE luhao_esign WITH ENCODING 'UTF8';

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    mobile VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(18) COMMENT '身份证号',
    status INTEGER DEFAULT 1 COMMENT '状态:1正常,0禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 企业表
CREATE TABLE enterprises (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '企业名称',
    credit_code VARCHAR(50) UNIQUE NOT NULL COMMENT '统一社会信用代码',
    legal_person VARCHAR(50) COMMENT '法人姓名',
    auth_status INTEGER DEFAULT 1 COMMENT '认证状态:1未认证,2认证中,3已认证',
    super_admin_id BIGINT REFERENCES users(id) COMMENT '超管用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    enterprise_id BIGINT REFERENCES enterprises(id) COMMENT '企业ID,0为系统角色',
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    permissions JSONB COMMENT '权限列表',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id BIGINT REFERENCES users(id),
    role_id BIGINT REFERENCES roles(id),
    enterprise_id BIGINT REFERENCES enterprises(id),
    PRIMARY KEY (user_id, role_id, enterprise_id)
);

-- 合同表
CREATE TABLE contracts (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL COMMENT '合同标题',
    enterprise_id BIGINT NOT NULL REFERENCES enterprises(id),
    initiator_id BIGINT NOT NULL REFERENCES users(id),
    status INTEGER DEFAULT 1 COMMENT '状态:1草稿,2签署中,3已完成,4已撤销',
    template_id VARCHAR(64) COMMENT '模板ID',
    content TEXT COMMENT '合同内容',
    file_path VARCHAR(255) COMMENT '文件路径',
    file_hash VARCHAR(64) COMMENT '文件哈希',
    deadline TIMESTAMP COMMENT '截止时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 合同签署方表
CREATE TABLE contract_signers (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL REFERENCES contracts(id),
    signer_type INTEGER NOT NULL COMMENT '签署方类型:1个人,2企业',
    signer_id BIGINT NOT NULL COMMENT '签署主体ID',
    actor_user_id BIGINT REFERENCES users(id) COMMENT '经办人ID',
    sign_order INTEGER DEFAULT 0 COMMENT '签署顺序',
    sign_status INTEGER DEFAULT 0 COMMENT '签署状态:0待签,1已签,2已拒',
    signed_at TIMESTAMP COMMENT '签署时间',
    sign_position JSONB COMMENT '签署位置信息'
);

-- 印章表
CREATE TABLE seals (
    id BIGSERIAL PRIMARY KEY,
    owner_id BIGINT NOT NULL COMMENT '所有者ID',
    owner_type INTEGER NOT NULL COMMENT '所有者类型:1个人,2企业',
    seal_type INTEGER NOT NULL COMMENT '印章类型:1个人签名,2公章,3合同章',
    name VARCHAR(50) NOT NULL COMMENT '印章名称',
    file_path VARCHAR(255) NOT NULL COMMENT '文件路径',
    status INTEGER DEFAULT 1 COMMENT '状态:1启用,0停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 存证记录表
CREATE TABLE evidence_chains (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT NOT NULL REFERENCES contracts(id),
    evidence_hash VARCHAR(64) NOT NULL COMMENT '存证哈希',
    blockchain_tx_hash VARCHAR(128) COMMENT '区块链交易哈希',
    timestamp_token TEXT COMMENT '时间戳令牌',
    status INTEGER DEFAULT 1 COMMENT '状态:1已存证,0存证失败',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_mobile ON users(mobile);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_enterprises_credit_code ON enterprises(credit_code);
CREATE INDEX idx_enterprises_auth_status ON enterprises(auth_status);
CREATE INDEX idx_contracts_enterprise_id ON contracts(enterprise_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_created_at ON contracts(created_at);
CREATE INDEX idx_contract_signers_contract_id ON contract_signers(contract_id);
CREATE INDEX idx_contract_signers_signer ON contract_signers(signer_id, signer_type);
CREATE INDEX idx_seals_owner ON seals(owner_id, owner_type);
CREATE INDEX idx_evidence_chains_contract_id ON evidence_chains(contract_id);
```

### 3. 实施路线图

**第一阶段：基础架构搭建（1-2个月）**
- 基础设施部署（K8s集群、数据库、中间件）
- 核心服务框架搭建（用户服务、企业服务、合同服务）
- API网关和服务注册发现
- 基础监控和日志系统

**第二阶段：核心业务功能（2-3个月）**
- 用户注册认证功能
- 企业认证和组织管理
- 合同基础功能（创建、签署、管理）
- 印章管理功能
- 基础AI功能（合同生成、审查）

**第三阶段：高级功能和优化（2-3个月）**
- 工作流引擎和审批功能
- 存证和证据链功能
- 高级AI功能（OCR、智能检索）
- 运营后台和数据分析
- 性能优化和压力测试

**第四阶段：完善和上线（1个月）**
- 安全加固和合规认证
- 全链路压力测试
- 灾备和容灾验证
- 生产环境部署和上线

**技术风险及应对策略**：
- **AI模型性能风险**：多模型备选方案，性能监控和自动切换
- **数据一致性风险**：分布式事务方案，补偿机制
- **高并发性能风险**：压力测试验证，自动扩缩容
- **安全合规风险**：安全审计，合规认证

### 4. 运维手册大纲

**部署流程SOP**
1. 环境准备检查清单
2. 服务部署步骤
3. 数据库初始化脚本
4. 配置文件模板
5. 健康检查验证

**监控告警配置清单**
1. 基础设施监控指标
2. 应用服务监控指标
3. 业务指标监控
4. 告警规则配置
5. 告警通知渠道

**故障排查手册**
1. 常见故障现象和解决方案
2. 日志查看和分析方法
3. 性能问题排查步骤
4. 数据库问题排查
5. 网络问题排查

**容量规划建议**
1. 服务器资源规划
2. 数据库容量规划
3. 存储容量规划
4. 网络带宽规划
5. 扩容触发条件

**安全加固检查清单**
1. 系统安全配置
2. 网络安全配置
3. 应用安全配置
4. 数据库安全配置
5. 定期安全检查项目

---

## 总结

本技术架构设计文档基于现有产品设计，采用Java+Go混合微服务架构，结合AI能力和区块链存证技术，构建了一个高可用、高性能、高安全的智能电子签名平台。

**核心技术亮点**：
1. **混合微服务架构**：Java处理复杂业务，Go处理高并发场景
2. **AI深度集成**：智能合同生成、审查、OCR识别等AI能力
3. **分布式存证**：区块链存证和时间戳服务保证法律效力
4. **云原生部署**：K8s容器化部署，自动扩缩容和故障恢复
5. **全链路监控**：Prometheus+Grafana+ELK完整监控体系

**预期性能指标**：
- 支持10万+ DAU，峰值QPS 10,000+
- 系统可用性99.9%，P99响应时间<1秒
- 支持PB级文件存储，千万级合同数据
- 具备水平扩展能力，可支撑业务快速增长

该架构设计充分考虑了业务需求、技术约束和非功能需求，为路浩智能电子签平台的成功实施提供了坚实的技术基础。
