# XXX系统技术架构设计Prompt

请基于以下PRD和功能列表，设计一个完整的技术架构方案：

## 需求理解

### 1. PRD概述
请用1-2句话概括产品核心业务场景（例如："一个支持多商户入驻的电商平台，用户可浏览商品、下单支付，商户可管理商品和订单"）。

### 2. 功能列表摘要  
列出核心功能模块（例如：用户管理、商品管理、订单系统、支付系统、后台管理）。

### 3. 关键业务流程
描述核心业务流程（例如："用户下单 → 支付 → 商户发货 → 用户确认收货 → 结算"）。

## 项目基础信息

**PRD概述：**
[请在此粘贴你的PRD核心内容，包括：
- 项目背景和目标
- 核心业务场景
- 用户角色定义
- 主要业务流程
- 关键业务指标]

**功能列表：**
[请在此详细列出功能模块，例如：
- 用户管理模块（注册、登录、权限管理）
- 核心业务模块1
- 核心业务模块2
- 数据统计分析模块
- 系统管理模块
等等...]

**业务规模预期：**
- 预期用户量：[如 10万+ DAU]
- 并发要求：[如 峰值5000+ QPS]  
- 数据规模：[如 千万级数据量]
- 可用性要求：[如 99.9%]

## 技术栈约束

### 后端技术栈
- **微服务框架：** 
  - Java：Spring Boot框架
  - Go：Kratos框架
- **数据存储：** 
  - 关系型数据库：PostgreSQL
  - 文档数据库：MongoDB
  - 缓存：Redis
  - 数据仓库：ClickHouse
- **中间件组件：**
  - 消息队列：RabbitMQ + Kafka
  - 服务注册发现：Nacos
  - 任务调度：XXL-Job
  - 搜索引擎：ElasticSearch

### 前端技术栈
- **构建工具：** Vite + Webpack
- **核心框架：** React + Next.js
- **样式方案：** Tailwind CSS
- **UI组件库：** Ant Design React + Chakra UI + ShadCN UI
- **状态管理：** Redux + Zustand
- **跨端开发：** Taro + UniApp

### 基础设施
- **容器技术：** Docker + Kubernetes
- **负载均衡：** SLB
- **链路追踪：** Zipkin
- **监控告警：** Prometheus + Grafana + Zabbix
- **日志系统：** ELK Stack + SLS

## 技术架构设计

### 1. 系统整体架构
- **架构风格选择**：选择架构风格（单体/微服务/无服务）及详细理由
- **系统组件图**：画出系统组件图（包含前端、后端服务、中间件、数据存储等），使用mermaid图示方式展示
- **服务拆分策略**：明确Java服务与Go服务的职责边界和选择依据
- **API网关设计**：统一入口、路由规则、限流熔断
- **服务通信**：同步调用（HTTP/gRPC）和异步消息机制
- **配置管理**：Nacos配置中心的使用策略
- **服务注册发现**：服务注册、健康检查、负载均衡

### 2. 技术选型
请详细说明技术选型理由和使用场景：

#### 后端服务选型
- **Java (Spring Boot) vs Go (Kratos)**：服务划分策略和选择依据
- **数据库选型**：
  - PostgreSQL：业务数据存储场景
  - MongoDB：文档数据存储场景
  - Redis：缓存策略设计
  - ClickHouse：数据分析场景
- **消息队列选型**：
  - RabbitMQ：实时业务消息场景
  - Kafka：大数据流处理场景
- **中间件组件**：
  - Nacos：服务注册发现和配置管理
  - XXL-Job：分布式任务调度
  - ElasticSearch：全文搜索和日志分析

#### 前端技术栈整合
- **构建工具整合**：Vite + Webpack的使用场景分工
- **UI组件库策略**：Ant Design + Chakra UI + ShadCN UI的整合方案
- **状态管理方案**：Redux vs Zustand的使用场景
- **跨端开发策略**：Taro vs UniApp的选择依据

#### 基础设施配置
- **容器编排**：Docker + Kubernetes部署策略
- **服务治理**：Nacos + Zipkin链路追踪
- **监控体系**：Prometheus + Grafana + Zabbix分层监控
- **日志体系**：ELK + SLS日志收集分析

### 3. 核心模块设计
- **模块职责**：详细描述关键模块的职责和交互方式（例如：订单服务如何与库存、支付服务协作）
- **流程图示**：设计中需要考虑采用mermaid图示方式展示流程

## 数据库设计

### 1. 数据模型
- **ER图设计**：画出ER图（实体关系图），标注主键、外键和关系类型，使用mermaid图示方式展示，参考示例：

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar username UK
        varchar email UK
        varchar password_hash
        integer status
        timestamp created_at
        timestamp updated_at
    }
    
    ORDERS {
        bigint id PK
        bigint user_id FK
        varchar order_no UK
        decimal total_amount
        varchar status
        timestamp created_at
        timestamp updated_at
    }
    
    ORDER_ITEMS {
        bigint id PK
        bigint order_id FK
        bigint product_id FK
        integer quantity
        decimal unit_price
        decimal total_price
    }
    
    PRODUCTS {
        bigint id PK
        varchar name
        varchar sku UK
        decimal price
        integer stock
        text description
        varchar status
        timestamp created_at
        timestamp updated_at
    }
    
    USERS ||--o{ ORDERS : "places"
    ORDERS ||--o{ ORDER_ITEMS : "contains"
    PRODUCTS ||--o{ ORDER_ITEMS : "ordered"
```

- **核心表结构**：列出核心数据表结构（表名、字段名、类型、约束、索引）
- **提供完整DDL**：提供核心表的完整建表语句

### 2. 数据流程  
- **业务数据流**：描述关键业务操作的数据流转（例如：用户注册 → 写入用户表 → 生成初始配置 → 触发通知），使用mermaid图示方式展示

### 3. 数据架构详细设计
#### PostgreSQL设计
- **核心业务表结构设计**：提供完整DDL语句，参考示例：
```sql
-- 用户表示例
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表示例
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    order_no VARCHAR(32) UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引示例
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

- **分库分表策略**：根据业务量级确定是否需要分库分表
- **索引优化方案**：主键索引、唯一索引、复合索引、部分索引设计
- **主从复制和读写分离配置**：主库写入、从库读取的配置方案
- **备份恢复策略**：定时备份、增量备份、PITR恢复

#### MongoDB设计  
- 文档结构设计和集合规划
- 索引策略和查询优化
- 分片集群配置
- 数据建模最佳实践

#### Redis设计
- 缓存架构设计（多级缓存）
- 数据结构选择和使用场景
- 过期策略和内存优化
- 缓存一致性保证机制
- 集群配置和高可用方案

#### ClickHouse设计
- 数据模型和表结构设计
- 分区策略和存储优化
- 物化视图设计
- ETL数据同步方案
- 查询性能优化

### 4. 消息队列架构
#### RabbitMQ设计
- **使用场景**：实时业务消息、任务队列、延时消息
- **Exchange和Queue设计**：交换机类型选择、队列命名规范
- **消息可靠性**：持久化、确认机制、重试策略
- **集群配置**：高可用集群搭建

#### Kafka设计
- **使用场景**：日志收集、大数据流处理、事件驱动
- **Topic设计**：分区策略、副本配置
- **消费者组**：并行消费、负载均衡
- **性能调优**：批量处理、压缩配置

### 5. 中间件集成
### 5. 中间件集成

#### Nacos集成
- **服务注册发现**：服务注册、健康检查、负载均衡
- **配置管理**：动态配置、配置热更新、环境隔离
- **命名空间设计**：多环境配置管理

#### XXL-Job任务调度
- **任务类型设计**：定时任务、延时任务、分片任务
- **调度策略**：Cron表达式、依赖调度
- **监控告警**：任务执行监控、失败告警

#### ElasticSearch集成
- **索引设计**：数据建模、分片策略
- **搜索场景**：全文搜索、聚合分析、日志查询
- **性能优化**：查询优化、集群调优

### 6. 前端架构设计

### 6. 前端架构设计

#### Web端架构
- **构建工具策略**：Vite vs Webpack的使用场景和配置
- **项目结构设计**：模块化组织、代码复用策略
- **UI组件库整合**：
  - Ant Design：企业级管理后台
  - Chakra UI：现代化用户界面
  - ShadCN UI：高度定制化组件
- **状态管理方案**：
  - Redux：复杂状态管理
  - Zustand：轻量级状态管理
- **路由设计**：Next.js路由、页面权限、懒加载策略
- **样式方案**：Tailwind CSS最佳实践

#### 移动端架构
- **跨端策略**：
  - Taro：微信生态应用
  - UniApp：多平台小程序和App
- **组件复用**：跨端组件设计原则
- **性能优化**：包体积优化、渲染性能
- **原生能力**：插件选择和自定义开发

### 7. Kubernetes部署架构
- **命名空间规划**：环境隔离、资源分组
- **工作负载设计**：Deployment、StatefulSet、DaemonSet使用
- **服务发现**：Service、Ingress配置
- **存储设计**：PV/PVC、存储类选择
- **配置管理**：ConfigMap、Secret使用
- **自动扩缩容**：HPA、VPA配置
- **滚动更新**：发布策略、回滚机制

### 6. 监控运维体系
### 8. 监控运维体系

#### 链路追踪：Zipkin
- **链路追踪集成**：微服务调用链路监控
- **性能分析**：接口响应时间、瓶颈识别
- **错误诊断**：异常链路分析

#### 指标监控：Prometheus + Grafana
- **监控指标设计**：业务指标、系统指标、JVM指标
- **告警规则配置**：阈值告警、趋势告警
- **仪表盘设计**：实时监控大屏、业务监控视图

#### 系统监控：Zabbix
- **主机监控**：CPU、内存、磁盘、网络
- **服务监控**：进程监控、端口监控
- **自动发现**：服务自动注册监控

#### 日志系统：ELK + SLS
- **日志收集**：Filebeat、Logstash配置
- **日志存储**：ElasticSearch索引设计
- **日志分析**：Kibana可视化、SLS日志分析
- **日志告警**：关键日志监控告警

## 非功能需求

### 1. 性能要求
请详细分析性能需求和优化方案：
- **性能指标预估**：
  - 预估QPS（每秒查询数）：核心接口、一般接口的QPS预期
  - 响应时间目标：P95、P99响应时间指标
  - 并发用户数：在线用户峰值预估
  - 吞吐量要求：数据处理能力要求
- **性能优化策略**：
  - **缓存优化**：多级缓存架构（Redis、本地缓存、浏览器缓存）
  - **CDN加速**：静态资源分发、动态内容加速
  - **异步处理**：消息队列异步、任务队列、事件驱动架构
  - **数据库优化**：索引设计、查询优化、读写分离、分库分表
  - **代码优化**：算法优化、连接池配置、线程池调优
- **压力测试方案**：性能基线测试、压力测试、稳定性测试

### 2. 安全要求
请提供全面的安全防护方案：
- **认证授权方案**：
  - **JWT方案**：Token设计、刷新机制、安全存储
  - **OAuth2.0方案**：第三方登录集成、授权码模式
  - **权限模型**：RBAC权限设计、细粒度权限控制
  - **会话管理**：会话超时、单点登录(SSO)
- **数据安全防护**：
  - **数据加密**：传输加密(HTTPS/TLS)、存储加密、敏感字段加密
  - **防SQL注入**：参数化查询、ORM框架使用、输入验证
  - **数据脱敏**：生产数据脱敏、日志脱敏
  - **备份加密**：数据备份加密存储
- **接口安全**：
  - **API安全**：接口签名、防重放攻击、限流熔断
  - **XSS防护**：输入过滤、输出编码
  - **CSRF防护**：Token验证、同源检查
- **网络安全**：
  - **防火墙规则**：端口管控、IP白名单
  - **VPC网络隔离**：内外网隔离、子网划分

### 3. 可扩展性设计
请设计应对业务增长的扩展方案：
- **水平扩展策略**：
  - **服务扩展**：微服务横向扩容、负载均衡配置
  - **数据库扩展**：读写分离、分库分表、数据分片
  - **缓存扩展**：Redis集群、缓存分片
  - **消息队列扩展**：分区扩容、集群扩展
- **垂直扩展策略**：
  - **硬件升级**：CPU、内存、存储升级方案
  - **性能调优**：JVM调优、数据库调优
- **架构演进**：
  - **单体到微服务**：渐进式拆分策略
  - **数据架构演进**：从单库到分布式数据库
  - **技术栈升级**：版本升级计划、技术迁移策略
- **容量规划**：
  - **业务增长预测**：用户增长、数据增长趋势
  - **资源规划**：服务器资源、存储资源预估
  - **成本控制**：弹性扩缩容、资源优化

### 4. 高可用性设计
请设计高可用架构方案：
- **可用性目标**：
  - **SLA指标**：99.9%、99.99%可用性目标
  - **故障恢复时间**：RTO（恢复时间目标）、RPO（恢复点目标）
- **冗余部署策略**：
  - **服务冗余**：多实例部署、跨AZ部署
  - **数据库冗余**：主从复制、多主复制、异地备份
  - **负载均衡**：多层负载均衡、健康检查
- **故障转移机制**：
  - **自动故障转移**：服务自动重启、流量自动切换
  - **数据库故障转移**：主从切换、读写分离切换
  - **缓存故障转移**：缓存降级、多级缓存
- **容灾方案**：
  - **同城容灾**：双机房部署、数据同步
  - **异地容灾**：跨地域备份、灾备中心建设
  - **业务连续性**：核心业务优先级、降级预案
- **监控告警**：
  - **实时监控**：服务健康检查、业务指标监控
  - **告警机制**：多级告警、告警升级、自动处理

### 5. 分布式系统设计
请详细介绍分布式系统设计原理和实现：
- **分布式系统原理**：
  - **CAP定理**：一致性、可用性、分区容错性的权衡
  - **BASE理论**：基本可用、软状态、最终一致性
  - **分布式算法**：Raft算法、Paxos算法原理
  - **时钟同步**：逻辑时钟、向量时钟、物理时钟同步
- **数据安全备份**：
  - **备份策略**：全量备份、增量备份、差异备份
  - **备份频率**：实时备份、定时备份策略
  - **备份存储**：本地备份、异地备份、云端备份
  - **备份验证**：备份完整性校验、恢复测试
  - **备份加密**：备份数据加密、传输加密
- **证据链存证设计**：
  - **存证流程**：数据上链、哈希计算、时间戳服务
  - **证据验证**：哈希验证、时间戳验证、完整性验证
  - **存证接口**：存证API设计、查证API设计
  - **法律效力**：存证标准、司法认可、合规要求
- **分布式一致性**：
  - **强一致性**：分布式锁、两阶段提交(2PC)
  - **最终一致性**：事件溯源、CQRS模式
  - **数据同步**：主从同步、多主同步、冲突解决
- **分布式事务**：
  - **分布式事务模式**：TCC、Saga、本地消息表
  - **事务协调器**：分布式事务管理器
  - **补偿机制**：事务回滚、补偿操作
- **分布式存储**：
  - **数据分片**：水平分片、垂直分片、一致性哈希
  - **副本管理**：多副本策略、副本一致性
  - **故障处理**：节点故障检测、数据迁移、自动恢复

## 交付物要求

### 1. 架构图
- **系统架构图**：使用mermaid图示方式绘制组件图、部署图
- **微服务拆分图**：服务间关系和通信方式
- **数据流向图**：关键业务数据流转路径

### 2. 数据库设计文档
- **数据库ER图**：使用mermaid图示方式展示实体关系，参考示例格式：

```mermaid
erDiagram
    USER_ENTITY {
        bigint id PK "主键ID"
        varchar username UK "用户名(唯一)"
        varchar email UK "邮箱(唯一)"
        varchar phone "手机号"
        integer status "状态:1正常,0禁用"
        timestamp created_at "创建时间"
    }
    
    ORDER_ENTITY {
        bigint id PK "订单ID"
        bigint user_id FK "用户ID"
        varchar order_no UK "订单号(唯一)"
        decimal total_amount "订单总金额"
        varchar status "订单状态"
        timestamp created_at "创建时间"
    }
    
    USER_ENTITY ||--o{ ORDER_ENTITY : "用户下单"
```

- **表结构DDL**：提供完整的建表语句，包含字段注释和约束
- **索引设计**：关键索引的创建语句和性能优化说明

### 3. 详细设计文档
- **服务清单**：列出所有微服务及其职责
- **接口设计**：核心API接口定义
- **配置示例**：关键组件的配置文件模板
- **业务流程图**：使用mermaid图示方式展示关键业务流程

### 4. 技术选型说明
- **服务清单**：列出所有微服务及其职责
- **数据库DDL**：提供核心表的完整建表语句
- **接口设计**：核心API接口定义
- **配置示例**：关键组件的配置文件模板

### 4. 实施路线图
- **第一阶段**：基础架构搭建（优先级高的核心服务）
- **第二阶段**：业务功能开发
- **第三阶段**：监控运维完善
- **技术风险**：识别潜在风险及应对策略
- **性能预期**：关键性能指标预估

### 6. 运维手册大纲
- 部署流程SOP
- 监控告警配置清单
- 故障排查手册
- 容量规划建议
- 安全加固检查清单

## 图表要求

请在设计方案中使用mermaid语法绘制以下图表：

### 1. 系统架构图
```mermaid
graph TB
    subgraph "前端层"
        A[Web应用] 
        B[移动应用]
    end
    subgraph "网关层"
        C[API网关]
    end
    subgraph "微服务层"
        D[用户服务-Java]
        E[订单服务-Java] 
        F[支付服务-Go]
        G[通知服务-Go]
    end
    subgraph "数据层"
        H[PostgreSQL]
        I[MongoDB]
        J[Redis]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    D --> H
    E --> H
    F --> I
    G --> J
```

### 2. 数据库ER图
```mermaid
erDiagram
    USER {
        int user_id PK
        string username
        string email
        datetime created_at
    }
    ORDER {
        int order_id PK
        int user_id FK
        decimal amount
        string status
        datetime created_at
    }
    USER ||--o{ ORDER : places
```

### 3. 业务流程图
```mermaid
sequenceDiagram
    participant U as 用户
    participant O as 订单服务
    participant P as 支付服务
    participant N as 通知服务
    
    U->>O: 创建订单
    O->>P: 发起支付
    P->>O: 支付成功
    O->>N: 发送通知
    N->>U: 订单确认
```

## 特别要求

1. **架构设计要充分考虑**：
   - 高可用性（99.9%+）
   - 高性能（支持预期并发）
   - 可扩展性（水平扩展能力）
   - 可维护性（模块化、文档化）

2. **实用性要求**：
   - 提供具体可执行的配置
   - 给出明确的技术选型依据
   - 包含详细的实施步骤

3. **技术深度要求**：
   - 不仅要有What，还要有Why和How
   - 考虑技术演进和迭代升级
   - 提供最佳实践建议

请基于以上要求，提供一个完整、详细、可落地的技术架构设计方案。
